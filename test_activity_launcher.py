import adbutils
import subprocess
import re
import time

def connect_to_device():
    """连接到ADB设备"""
    print("正在连接到 ADB 设备...")
    try:
        # 使用本地ADB服务器
        adb = adbutils.AdbClient(host="127.0.0.1", port=5037)
        print(f"ADB 客户端已创建")
        
        # 获取设备列表
        devices = adb.device_list()
        print(f"发现 {len(devices)} 个设备")
        
        if not devices:
            print("❌ 没有发现任何设备！")
            print("请先连接设备: adb connect <IP:PORT>")
            return None
        
        device = adb.device(devices[0])
        print(f"✅ 成功连接到设备: {devices[0]}")
        
        # 测试设备连接
        test_result = device.shell("echo 'ADB连接测试成功'")
        print(f"设备响应: {test_result.strip()}")
        
        return device
        
    except Exception as e:
        print(f"❌ ADB 连接失败: {e}")
        return None

def get_installed_packages(device):
    """获取所有已安装包名"""
    result = device.shell("pm list packages")
    packages = [line.replace("package:", "").strip() for line in result.splitlines()]
    return packages

def get_app_activities(device, pkg):
    """获取应用的所有Activity"""
    try:
        # 使用 aapt 命令获取 Activity
        result = device.shell(f"dumpsys package {pkg}")
        activities = []
        
        # 查找 Activity 信息
        lines = result.splitlines()
        for i, line in enumerate(lines):
            if "Activity #" in line and pkg in line:
                # 提取Activity名称
                parts = line.split()
                for part in parts:
                    if "/" in part and pkg in part:
                        activities.append(part)
                        break
        
        # 如果没找到，尝试另一种方法
        if not activities:
            result = device.shell(f"cmd package dump {pkg}")
            for line in result.splitlines():
                if "android.intent.action.MAIN" in line:
                    # 查找前面的Activity声明
                    for j in range(max(0, i-10), i):
                        if j < len(lines) and "Activity #" in lines[j]:
                            parts = lines[j].split()
                            for part in parts:
                                if "/" in part and pkg in part:
                                    activities.append(part)
                                    break
        
        return list(set(activities))  # 去重
    except Exception as e:
        print(f"获取 {pkg} 的Activity失败: {e}")
        return []

def start_activity(device, activity_name):
    """启动指定的Activity"""
    try:
        print(f"  启动: {activity_name}")
        result = device.shell(f"am start {activity_name}")
        success = "Starting:" in result or "Success" in result
        if success:
            print(f"    ✅ 成功")
        else:
            print(f"    ❌ 失败: {result.strip()}")
        return success
    except Exception as e:
        print(f"    ❌ 启动失败: {e}")
        return False

def test_specific_app():
    """测试启动指定应用的Activity"""
    device = connect_to_device()
    if not device:
        return
    
    # 测试启动指定应用
    test_packages = [
        "com.xiaopeng.devtools",
        "com.android.settings", 
        "com.android.calculator2",
        "com.wow.carlauncher.mini"
    ]
    
    packages = get_installed_packages(device)
    
    for pkg in test_packages:
        if pkg in packages:
            print(f"\n🔍 测试应用: {pkg}")
            activities = get_app_activities(device, pkg)
            
            if activities:
                print(f"发现 {len(activities)} 个Activity:")
                for activity in activities[:3]:  # 只测试前3个
                    start_activity(device, activity)
                    time.sleep(1)  # 等待1秒
            else:
                print("未找到Activity")
                # 尝试启动主Activity
                print("尝试启动主Activity...")
                start_activity(device, f"{pkg}/.MainActivity")
        else:
            print(f"❌ 应用 {pkg} 未安装")

def manual_activity_test():
    """手动测试Activity启动"""
    device = connect_to_device()
    if not device:
        return
    
    print("\n🧪 手动Activity测试")
    print("输入要启动的Activity (格式: com.package/.ActivityName)")
    print("输入 'q' 退出")
    
    while True:
        activity = input("\n请输入Activity: ").strip()
        if activity.lower() == 'q':
            break
        
        if "/" in activity:
            start_activity(device, activity)
        else:
            print("❌ 格式错误，请使用 com.package/.ActivityName 格式")

if __name__ == "__main__":
    print("🔧 Activity启动测试工具")
    print("1. 测试指定应用")
    print("2. 手动测试Activity")
    
    choice = input("请选择 (1/2): ").strip()
    
    if choice == "1":
        test_specific_app()
    elif choice == "2":
        manual_activity_test()
    else:
        print("无效选择")
