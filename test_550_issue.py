#!/usr/bin/env python3
"""
测试550停止问题的脚本
"""

import sys
import time
from colorama import init, For<PERSON>

def simulate_test_loop():
    """模拟测试循环，检查550停止问题"""
    init()
    
    print(Fore.CYAN + "=" * 60)
    print(Fore.CYAN + "      测试550停止问题的模拟")
    print(Fore.CYAN + "=" * 60)
    
    # 模拟参数
    total_count = 1000
    reconnect_interval = 50
    max_reconnects = 10
    
    successful_tests = 0
    failed_tests = 0
    reconnect_count = 0
    
    print(Fore.WHITE + f"总测试数量: {total_count}")
    print(Fore.WHITE + f"重连检查间隔: 每{reconnect_interval}个测试")
    print(Fore.WHITE + f"最大重连次数: {max_reconnects}")
    print(Fore.CYAN + "=" * 60)
    
    for packet_num in range(1, total_count + 1):
        # 模拟进度显示
        if packet_num % 100 == 0:
            progress = (packet_num / total_count) * 100
            print(Fore.MAGENTA + f"[  进度  ] {packet_num}/{total_count} ({progress:.1f}%) - 成功:{successful_tests} 失败:{failed_tests} 重连:{reconnect_count}")
        
        # 模拟定期连接检查
        if packet_num % reconnect_interval == 0:
            print(Fore.CYAN + f"[  {packet_num:04d}  ] 定期连接检查...")
            
            # 模拟连接断开的情况（每200个测试模拟一次连接问题）
            if packet_num % 200 == 0:
                print(Fore.YELLOW + f"[  {packet_num:04d}  ] 模拟连接断开")
                
                if reconnect_count >= max_reconnects:
                    # 旧逻辑：这里会break退出
                    print(Fore.YELLOW + f"[  警告  ] 已达到最大重连次数 ({max_reconnects})")
                    print(Fore.YELLOW + f"[  {packet_num:04d}  ] 新逻辑：继续测试，但可能会有更多连接错误")
                    # 不再break，继续测试
                else:
                    reconnect_count += 1
                    print(Fore.GREEN + f"[  {packet_num:04d}  ] 模拟重连成功 (重连次数: {reconnect_count})")
            else:
                print(Fore.GREEN + f"[  {packet_num:04d}  ] 连接状态正常")
        
        # 模拟测试成功
        successful_tests += 1
        
        # 快速模拟，不需要真实延迟
        if packet_num % 50 == 0:
            time.sleep(0.01)  # 很小的延迟让用户看到进度
    
    # 最终统计
    print(Fore.CYAN + "\n" + "=" * 60)
    print(Fore.CYAN + "           模拟测试完成统计")
    print(Fore.CYAN + "=" * 60)
    print(Fore.GREEN + f"成功测试: {successful_tests}")
    print(Fore.RED + f"失败测试: {failed_tests}")
    print(Fore.WHITE + f"总计测试: {successful_tests + failed_tests}")
    print(Fore.YELLOW + f"重连次数: {reconnect_count}")
    
    if successful_tests + failed_tests == total_count:
        print(Fore.GREEN + "✓ 测试完成：所有测试用例都已执行")
        print(Fore.GREEN + "✓ 修复成功：不再在550处停止")
    else:
        print(Fore.RED + "✗ 测试未完成：存在提前退出问题")
    
    print(Fore.CYAN + "=" * 60)

def analyze_550_issue():
    """分析550停止问题"""
    print(Fore.CYAN + "\n--- 550停止问题分析 ---")
    print(Fore.WHITE + "问题原因：")
    print(Fore.YELLOW + "1. 默认重连检查间隔：50个测试")
    print(Fore.YELLOW + "2. 默认最大重连次数：10次")
    print(Fore.YELLOW + "3. 550 = 50 × 11，在第550个测试时进行第11次检查")
    print(Fore.YELLOW + "4. 如果此时连接断开且已重连10次，旧逻辑会退出")
    
    print(Fore.WHITE + "\n修复方案：")
    print(Fore.GREEN + "1. 达到最大重连次数时不再退出测试")
    print(Fore.GREEN + "2. 改为跳过重连，继续测试")
    print(Fore.GREEN + "3. 添加警告信息，但不中断测试流程")
    print(Fore.GREEN + "4. 允许用户选择是否在达到最大重连次数后继续")

def main():
    print(Fore.CYAN + "DoIP模糊测试工具 - 550停止问题测试")
    print(Fore.WHITE + "1. 运行模拟测试")
    print(Fore.WHITE + "2. 查看问题分析")
    print(Fore.WHITE + "3. 退出")
    
    choice = input(Fore.YELLOW + "请选择 (1-3): ").strip()
    
    if choice == "1":
        simulate_test_loop()
    elif choice == "2":
        analyze_550_issue()
    elif choice == "3":
        print(Fore.GREEN + "退出程序")
    else:
        print(Fore.RED + "无效选择")

if __name__ == "__main__":
    main()
