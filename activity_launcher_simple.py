import adbutils
import time

def connect_device():
    """连接设备"""
    try:
        adb = adbutils.AdbClient(host="127.0.0.1", port=5037)
        devices = adb.device_list()
        
        if not devices:
            print("❌ 没有发现设备")
            return None
        
        device = devices[0]  # 使用第一个设备
        print(f"✅ 连接到设备: {device.serial}")
        return device
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return None

def get_packages(device):
    """获取应用包列表"""
    result = device.shell("pm list packages")
    packages = [line.replace("package:", "").strip() for line in result.splitlines()]
    return packages

def get_activities(device, package):
    """获取应用的Activity"""
    try:
        # 使用dumpsys获取Activity信息
        result = device.shell(f"dumpsys package {package}")
        activities = []
        
        lines = result.splitlines()
        for line in lines:
            if "Activity #" in line and package in line:
                parts = line.split()
                for part in parts:
                    if "/" in part and package in part:
                        activities.append(part)
                        break
        
        return list(set(activities))
    except Exception as e:
        print(f"获取Activity失败: {e}")
        return []

def launch_activity(device, activity):
    """启动Activity"""
    try:
        result = device.shell(f"am start {activity}")
        success = "Starting:" in result or "Success" in result
        return success, result.strip()
    except Exception as e:
        return False, str(e)

def main():
    print("🚀 Android Activity启动工具")
    print("=" * 50)
    
    # 连接设备
    device = connect_device()
    if not device:
        return
    
    # 获取应用列表
    print("获取应用列表...")
    packages = get_packages(device)
    print(f"发现 {len(packages)} 个应用")
    
    # 显示一些常见应用
    common_apps = [
        "com.android.settings",
        "com.xiaopeng.devtools", 
        "com.wow.carlauncher.mini",
        "com.android.bluetooth",
        "com.xiaopeng.xmart.camera"
    ]
    
    print("\n常见应用:")
    available_apps = []
    for i, app in enumerate(common_apps, 1):
        if app in packages:
            print(f"{i}. {app} ✅")
            available_apps.append(app)
        else:
            print(f"{i}. {app} ❌")
    
    if not available_apps:
        print("没有找到常见应用")
        return
    
    # 选择应用
    print(f"\n选择要测试的应用 (1-{len(available_apps)}):")
    try:
        choice = int(input("请输入编号: ")) - 1
        if 0 <= choice < len(available_apps):
            selected_app = available_apps[choice]
        else:
            print("无效选择")
            return
    except ValueError:
        print("无效输入")
        return
    
    print(f"\n🔍 分析应用: {selected_app}")
    
    # 获取Activity
    activities = get_activities(device, selected_app)
    if not activities:
        print("❌ 未找到Activity")
        # 尝试启动主Activity
        print("尝试启动主Activity...")
        main_activity = f"{selected_app}/.MainActivity"
        success, result = launch_activity(device, main_activity)
        if success:
            print(f"✅ 成功启动: {main_activity}")
        else:
            print(f"❌ 启动失败: {result}")
        return
    
    print(f"发现 {len(activities)} 个Activity:")
    for i, activity in enumerate(activities, 1):
        print(f"{i}. {activity}")
    
    # 启动选项
    print(f"\n启动选项:")
    print("a - 启动所有Activity")
    print("数字 - 启动指定Activity")
    print("q - 退出")
    
    choice = input("请选择: ").strip().lower()
    
    if choice == 'q':
        return
    elif choice == 'a':
        print(f"\n🚀 启动所有Activity...")
        successful = 0
        for activity in activities:
            print(f"启动: {activity}")
            success, result = launch_activity(device, activity)
            if success:
                successful += 1
                print("  ✅ 成功")
            else:
                print(f"  ❌ 失败: {result}")
            time.sleep(1)  # 等待1秒
        print(f"\n完成! 成功启动 {successful}/{len(activities)} 个Activity")
    elif choice.isdigit():
        idx = int(choice) - 1
        if 0 <= idx < len(activities):
            activity = activities[idx]
            print(f"\n🚀 启动: {activity}")
            success, result = launch_activity(device, activity)
            if success:
                print("✅ 启动成功!")
            else:
                print(f"❌ 启动失败: {result}")
        else:
            print("无效的Activity编号")
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
