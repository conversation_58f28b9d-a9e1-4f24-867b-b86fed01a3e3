# Android 权限分析与Activity启动工具

一个功能强大的Android应用权限分析和Activity启动工具，支持批量分析、交互式检测和自动化Activity启动。

## 🚀 新增功能特性

### 1. 🔍 交互式权限检测
- **分页浏览**: 支持分页浏览所有已安装应用
- **选择性分析**: 可选择特定应用进行详细权限分析
- **权限分类**: 分别显示已授权和未授权权限
- **实时统计**: 显示权限数量统计信息

### 2. 🚀 Activity启动功能
- **自动发现**: 自动发现应用的所有Activity组件
- **选择性启动**: 支持启动指定应用的特定Activity
- **批量启动**: 支持批量启动所有应用的所有Activity
- **启动日志**: 详细记录启动成功/失败状态

### 3. 📊 增强的报告功能
- **详细报告**: 生成完整的权限分析报告（~27000行）
- **摘要报告**: 生成简洁的统计摘要（~60行）
- **Activity日志**: 生成Activity启动详细日志
- **高风险应用**: 自动识别权限过度声明的应用

### 4. 🔧 改进的设备连接
- **自动检测**: 自动检测可用的ADB设备
- **多设备支持**: 支持多设备环境下的设备选择
- **错误处理**: 更好的连接错误处理和提示

## 📋 功能菜单

```
🔧 Android 应用权限分析与Activity启动工具
============================================================
1. 详细权限分析 (生成完整报告)
2. 摘要权限分析 (生成简洁摘要)
3. 交互式权限检测 (选择特定应用)
4. 启动指定应用的Activity
5. 启动所有应用的所有Activity (⚠️ 慎用)
6. 退出程序
```

## 🛠️ 安装要求

```bash
pip install adbutils
```

## 📱 设备连接

1. 确保Android设备开启USB调试
2. 连接设备到ADB：
```bash
adb connect <设备IP>:5555
```
3. 验证连接：
```bash
adb devices
```

## 💡 使用示例

### 启动特定Activity
```bash
adb shell am start com.xiaopeng.devtools/.view.log.GrabLogActivity
```

### 获取应用权限信息
```bash
adb shell dumpsys package com.example.app
```

### 列出所有应用
```bash
adb shell pm list packages
```

## 📊 分析结果示例

### 权限统计摘要
```
📊 权限分析摘要:
总应用数: 240
有未授权权限的应用数: 180
权限授权率: 75.4%
高风险应用数: 21
```

### 高风险应用
```
⚠️ 高风险应用 (声明权限>50个)
----------------------------------------
📦 com.android.shell
   声明: 489 | 已授权: 1056 | 未授权: 489
📦 com.android.systemui  
   声明: 174 | 已授权: 344 | 未授权: 174
```

### Activity启动统计
```
📊 启动统计:
总Activity数: 127
成功启动: 98
启动失败: 29
成功率: 77.2%
```

## 📁 生成文件

- `android_permissions_analysis_YYYYMMDD_HHMMSS.txt` - 详细权限分析报告
- `android_permissions_summary_YYYYMMDD_HHMMSS.txt` - 权限分析摘要
- `activity_launch_log_YYYYMMDD_HHMMSS.txt` - Activity启动日志

## ⚠️ 注意事项

1. **批量Activity启动**: 可能影响设备性能，建议谨慎使用
2. **权限要求**: 需要ADB调试权限
3. **网络连接**: 确保设备与电脑在同一网络
4. **设备兼容性**: 支持Android 5.0+设备

## 🔧 故障排除

### 连接问题
- 检查设备IP地址是否正确
- 确认USB调试已开启
- 验证网络连接状态

### 权限问题
- 确保ADB有足够权限
- 检查设备授权状态
- 重新连接设备

### Activity启动失败
- 检查Activity名称格式
- 确认应用已安装
- 验证Activity是否可导出

## 📈 更新日志

### v2.0 (2025-08-27)
- ✨ 新增交互式权限检测功能
- 🚀 新增Activity启动功能
- 📊 增强报告生成功能
- 🔧 改进设备连接机制
- 🐛 修复权限解析问题

### v1.0 (2025-08-26)
- 🎉 初始版本发布
- 📱 基础权限分析功能
- 📄 报告生成功能

## 📞 技术支持

如遇问题，请检查：
1. ADB连接状态
2. 设备权限设置
3. 网络连接状况
4. 应用安装状态
