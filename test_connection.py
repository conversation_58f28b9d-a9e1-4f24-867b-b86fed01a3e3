import adbutils

def test_connection():
    """测试ADB连接"""
    print("正在测试 ADB 连接...")
    
    try:
        # 创建ADB客户端
        adb = adbutils.AdbClient(host="127.0.0.1", port=5037)
        print("✅ ADB 客户端已创建")
        
        # 获取设备列表
        devices = adb.device_list()
        print(f"发现 {len(devices)} 个设备")
        
        if not devices:
            print("❌ 没有发现任何设备")
            print("请使用以下命令连接设备:")
            print("adb connect <设备IP>:5555")
            return None
        
        # 显示设备信息
        for i, device_info in enumerate(devices, 1):
            print(f"{i}. 设备信息: {device_info}")
            print(f"   类型: {type(device_info)}")
            
            # 尝试提取设备序列号
            if hasattr(device_info, 'serial'):
                serial = device_info.serial
                print(f"   序列号: {serial}")
            else:
                serial = str(device_info)
                print(f"   字符串表示: {serial}")
        
        # 使用第一个设备
        device_info = devices[0]
        if hasattr(device_info, 'serial'):
            serial = device_info.serial
        else:
            serial = str(device_info)
        
        print(f"\n正在连接到设备: {serial}")
        
        # 创建设备连接
        device = adb.device(serial)
        print(f"✅ 设备对象已创建: {device}")
        
        # 测试设备通信
        result = device.shell("echo 'Hello ADB'")
        print(f"✅ 设备响应: {result.strip()}")
        
        # 测试获取包列表
        print("\n测试获取应用包列表...")
        packages_result = device.shell("pm list packages | head -5")
        print("前5个应用包:")
        print(packages_result)
        
        return device
        
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_activity_launch(device):
    """测试Activity启动"""
    if not device:
        print("❌ 设备未连接，无法测试Activity启动")
        return
    
    print("\n🚀 测试Activity启动...")
    
    # 测试启动设置应用
    test_activities = [
        "com.android.settings/.Settings",
        "com.android.settings/.wifi.WifiSettings",
        "com.xiaopeng.devtools/.MainActivity"
    ]
    
    for activity in test_activities:
        try:
            print(f"尝试启动: {activity}")
            result = device.shell(f"am start {activity}")
            if "Starting:" in result or "Success" in result:
                print(f"  ✅ 启动成功")
            else:
                print(f"  ❌ 启动失败: {result.strip()}")
        except Exception as e:
            print(f"  ❌ 启动异常: {e}")

if __name__ == "__main__":
    print("🔧 ADB连接测试工具")
    print("=" * 50)
    
    device = test_connection()
    
    if device:
        print("\n" + "=" * 50)
        choice = input("是否测试Activity启动? (y/N): ").strip().lower()
        if choice == 'y':
            test_activity_launch(device)
    
    print("\n测试完成!")
