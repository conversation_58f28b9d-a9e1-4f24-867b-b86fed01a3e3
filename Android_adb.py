import adbutils
import subprocess
import re

# 初始化 ADB 设备
print("正在连接到 ADB 设备...")
try:
    # 直接连接到已知设备
    adb = adbutils.AdbClient(host="127.0.0.1", port=5037)  # 使用本地ADB服务器
    print(f"ADB 客户端已创建")

    # 直接使用设备地址
    device = adb.device("**************:5555")
    print(f"✅ 成功连接到设备: **************:5555")

    # 测试设备连接
    test_result = device.shell("echo 'ADB连接测试成功'")
    print(f"设备响应: {test_result.strip()}")

except Exception as e:
    print(f"❌ ADB 连接失败: {e}")
    print("请检查设备连接和网络设置")
    exit(1)

def get_installed_packages():
    """获取所有已安装包名"""
    result = device.shell("pm list packages")
    packages = [line.replace("package:", "").strip() for line in result.splitlines()]
    return packages

def get_declared_permissions(pkg):
    """获取 Manifest 中声明的权限"""
    result = device.shell(f"dumpsys package {pkg}")
    permissions = []
    capture = False
    for line in result.splitlines():
        if "requested permissions:" in line:
            capture = True
            continue
        if capture:
            # 遇到空行或其他section开始时停止
            if line.strip() == "" or (":" in line and not line.strip().startswith("android.permission")):
                break
            # 提取权限名称，过滤掉非权限行
            line = line.strip()
            if line.startswith("android.permission") or line.startswith("com.") or line.startswith("car.permission"):
                permissions.append(line)
    return permissions

def get_granted_permissions(pkg):
    """获取当前已授权的权限"""
    result = device.shell(f"dumpsys package {pkg}")
    granted = []

    # 查找install permissions部分
    lines = result.splitlines()
    in_install_permissions = False
    in_runtime_permissions = False

    for line in lines:
        line = line.strip()

        # 检测install permissions部分
        if "install permissions:" in line:
            in_install_permissions = True
            continue
        elif "runtime permissions:" in line:
            in_runtime_permissions = True
            in_install_permissions = False
            continue
        elif line.endswith(":") and ("permissions" in line or "User" in line):
            in_install_permissions = False
            in_runtime_permissions = False
            continue

        # 解析install permissions
        if in_install_permissions and line:
            # 格式通常是: android.permission.XXX: granted=true
            if "granted=true" in line:
                perm_match = re.search(r'(android\.permission\.\S+|com\.\S+|car\.permission\.\S+)', line)
                if perm_match:
                    granted.append(perm_match.group(1))

        # 解析runtime permissions
        if in_runtime_permissions and line:
            if "granted=true" in line:
                perm_match = re.search(r'(android\.permission\.\S+|com\.\S+|car\.permission\.\S+)', line)
                if perm_match:
                    granted.append(perm_match.group(1))

    return granted

def analyze_permissions():
    packages = get_installed_packages()
    print(f"共发现 {len(packages)} 个应用，开始分析权限...\n")

    # 准备保存结果到文件
    import datetime
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"android_permissions_analysis_{timestamp}.txt"

    with open(output_file, 'w', encoding='utf-8') as f:
        # 写入文件头
        f.write(f"Android 应用权限分析报告\n")
        f.write(f"分析时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"共发现 {len(packages)} 个应用\n")
        f.write("=" * 80 + "\n\n")

        for pkg in packages:
            declared = get_declared_permissions(pkg)
            granted = get_granted_permissions(pkg)
            overclaimed = set(declared) - set(granted)
            granted_clean = set(granted)  # 已授权的权限

            # 控制台输出
            print(f"📦 应用：{pkg}")
            print(f"  - 声明权限数：{len(declared)}")
            print(f"  - 已授权权限数：{len(granted)}")

            if granted_clean:
                print(f"  ✅ 已授权的权限：")
                for p in sorted(granted_clean):
                    print(f"    - {p}")

            if overclaimed:
                print(f"  ⚠️ 未授权但声明的权限：")
                for p in sorted(overclaimed):
                    print(f"    - {p}")
            print("-" * 40)

            # 文件输出
            f.write(f"应用：{pkg}\n")
            f.write(f"声明权限数：{len(declared)}\n")
            f.write(f"已授权权限数：{len(granted)}\n")
            f.write(f"未授权权限数：{len(overclaimed)}\n\n")

            if granted_clean:
                f.write("已授权的权限：\n")
                for p in sorted(granted_clean):
                    f.write(f"  - {p}\n")
                f.write("\n")

            if overclaimed:
                f.write("未授权但声明的权限：\n")
                for p in sorted(overclaimed):
                    f.write(f"  - {p}\n")
                f.write("\n")

            f.write("-" * 80 + "\n\n")

    print(f"\n✅ 分析完成！结果已保存到文件：{output_file}")

def generate_summary_report():
    """生成权限分析摘要报告"""
    packages = get_installed_packages()
    print(f"正在生成摘要报告...")

    import datetime
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    summary_file = f"android_permissions_summary_{timestamp}.txt"

    total_apps = len(packages)
    apps_with_overclaimed = 0
    total_declared = 0
    total_granted = 0
    total_overclaimed = 0

    high_risk_apps = []  # 声明权限过多的应用

    with open(summary_file, 'w', encoding='utf-8') as f:
        f.write(f"Android 应用权限分析摘要报告\n")
        f.write(f"分析时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"共分析 {total_apps} 个应用\n")
        f.write("=" * 80 + "\n\n")

        for pkg in packages:
            declared = get_declared_permissions(pkg)
            granted = get_granted_permissions(pkg)
            overclaimed = set(declared) - set(granted)

            total_declared += len(declared)
            total_granted += len(granted)
            total_overclaimed += len(overclaimed)

            if overclaimed:
                apps_with_overclaimed += 1

            # 标记高风险应用（声明权限超过50个）
            if len(declared) > 50:
                high_risk_apps.append({
                    'pkg': pkg,
                    'declared': len(declared),
                    'granted': len(granted),
                    'overclaimed': len(overclaimed)
                })

        # 写入统计摘要
        f.write("📊 权限使用统计\n")
        f.write("-" * 40 + "\n")
        f.write(f"总应用数: {total_apps}\n")
        f.write(f"有未授权权限的应用数: {apps_with_overclaimed}\n")
        f.write(f"总声明权限数: {total_declared}\n")
        f.write(f"总已授权权限数: {total_granted}\n")
        f.write(f"总未授权权限数: {total_overclaimed}\n")
        f.write(f"权限授权率: {(total_granted/total_declared*100):.1f}%\n\n")

        # 写入高风险应用
        f.write("⚠️ 高风险应用 (声明权限>50个)\n")
        f.write("-" * 40 + "\n")
        high_risk_apps.sort(key=lambda x: x['declared'], reverse=True)
        for app in high_risk_apps[:20]:  # 只显示前20个
            f.write(f"📦 {app['pkg']}\n")
            f.write(f"   声明: {app['declared']} | 已授权: {app['granted']} | 未授权: {app['overclaimed']}\n")
        f.write(f"\n共发现 {len(high_risk_apps)} 个高风险应用\n\n")

        # 控制台输出摘要
        print(f"\n📊 权限分析摘要:")
        print(f"总应用数: {total_apps}")
        print(f"有未授权权限的应用数: {apps_with_overclaimed}")
        print(f"权限授权率: {(total_granted/total_declared*100):.1f}%")
        print(f"高风险应用数: {len(high_risk_apps)}")

    print(f"\n✅ 摘要报告已保存到: {summary_file}")

if __name__ == "__main__":
    print("选择分析模式:")
    print("1. 详细分析 (生成完整报告)")
    print("2. 摘要分析 (生成简洁摘要)")

    choice = input("请输入选择 (1/2): ").strip()

    if choice == "2":
        generate_summary_report()
    else:
        analyze_permissions()
