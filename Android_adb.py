import adbutils
import subprocess
import re

# 初始化 ADB 设备
def connect_to_device():
    """连接到ADB设备"""
    print("正在连接到 ADB 设备...")
    try:
        # 使用本地ADB服务器
        adb = adbutils.AdbClient(host="127.0.0.1", port=5037)
        print(f"ADB 客户端已创建")

        # 获取设备列表
        devices = adb.device_list()
        print(f"发现 {len(devices)} 个设备")

        if not devices:
            print("❌ 没有发现任何设备！")
            print("请确保:")
            print("1. 设备已连接并开启 ADB 调试")
            print("2. 使用 'adb connect <IP:PORT>' 连接设备")
            print("3. 网络连接正常")
            return None

        # 显示所有设备并让用户选择
        if len(devices) == 1:
            device_serial = devices[0]
        else:
            print("发现多个设备:")
            for i, dev in enumerate(devices, 1):
                print(f"{i}. {dev}")
            choice = input("请选择设备编号 (默认1): ").strip()
            if choice.isdigit() and 1 <= int(choice) <= len(devices):
                device_serial = devices[int(choice) - 1]
            else:
                device_serial = devices[0]

        device = adb.device(device_serial)
        print(f"✅ 成功连接到设备: {device_serial}")

        # 测试设备连接
        test_result = device.shell("echo 'ADB连接测试成功'")
        print(f"设备响应: {test_result.strip()}")

        return device

    except Exception as e:
        print(f"❌ ADB 连接失败: {e}")
        print("请检查设备连接和网络设置")
        return None

# 连接设备
device = connect_to_device()
if device is None:
    print("无法连接到设备，程序退出")
    exit(1)

def get_installed_packages():
    """获取所有已安装包名"""
    result = device.shell("pm list packages")
    packages = [line.replace("package:", "").strip() for line in result.splitlines()]
    return packages

def get_declared_permissions(pkg):
    """获取 Manifest 中声明的权限"""
    result = device.shell(f"dumpsys package {pkg}")
    permissions = []
    capture = False
    for line in result.splitlines():
        if "requested permissions:" in line:
            capture = True
            continue
        if capture:
            # 遇到空行或其他section开始时停止
            if line.strip() == "" or (":" in line and not line.strip().startswith("android.permission")):
                break
            # 提取权限名称，过滤掉非权限行
            line = line.strip()
            if line.startswith("android.permission") or line.startswith("com.") or line.startswith("car.permission"):
                permissions.append(line)
    return permissions

def get_granted_permissions(pkg):
    """获取当前已授权的权限"""
    result = device.shell(f"dumpsys package {pkg}")
    granted = []

    # 查找install permissions部分
    lines = result.splitlines()
    in_install_permissions = False
    in_runtime_permissions = False

    for line in lines:
        line = line.strip()

        # 检测install permissions部分
        if "install permissions:" in line:
            in_install_permissions = True
            continue
        elif "runtime permissions:" in line:
            in_runtime_permissions = True
            in_install_permissions = False
            continue
        elif line.endswith(":") and ("permissions" in line or "User" in line):
            in_install_permissions = False
            in_runtime_permissions = False
            continue

        # 解析install permissions
        if in_install_permissions and line:
            # 格式通常是: android.permission.XXX: granted=true
            if "granted=true" in line:
                perm_match = re.search(r'(android\.permission\.\S+|com\.\S+|car\.permission\.\S+)', line)
                if perm_match:
                    granted.append(perm_match.group(1))

        # 解析runtime permissions
        if in_runtime_permissions and line:
            if "granted=true" in line:
                perm_match = re.search(r'(android\.permission\.\S+|com\.\S+|car\.permission\.\S+)', line)
                if perm_match:
                    granted.append(perm_match.group(1))

    return granted

def analyze_permissions():
    packages = get_installed_packages()
    print(f"共发现 {len(packages)} 个应用，开始分析权限...\n")

    # 准备保存结果到文件
    import datetime
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"android_permissions_analysis_{timestamp}.txt"

    with open(output_file, 'w', encoding='utf-8') as f:
        # 写入文件头
        f.write(f"Android 应用权限分析报告\n")
        f.write(f"分析时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"共发现 {len(packages)} 个应用\n")
        f.write("=" * 80 + "\n\n")

        for pkg in packages:
            declared = get_declared_permissions(pkg)
            granted = get_granted_permissions(pkg)
            overclaimed = set(declared) - set(granted)
            granted_clean = set(granted)  # 已授权的权限

            # 控制台输出
            print(f"📦 应用：{pkg}")
            print(f"  - 声明权限数：{len(declared)}")
            print(f"  - 已授权权限数：{len(granted)}")

            if granted_clean:
                print(f"  ✅ 已授权的权限：")
                for p in sorted(granted_clean):
                    print(f"    - {p}")

            if overclaimed:
                print(f"  ⚠️ 未授权但声明的权限：")
                for p in sorted(overclaimed):
                    print(f"    - {p}")
            print("-" * 40)

            # 文件输出
            f.write(f"应用：{pkg}\n")
            f.write(f"声明权限数：{len(declared)}\n")
            f.write(f"已授权权限数：{len(granted)}\n")
            f.write(f"未授权权限数：{len(overclaimed)}\n\n")

            if granted_clean:
                f.write("已授权的权限：\n")
                for p in sorted(granted_clean):
                    f.write(f"  - {p}\n")
                f.write("\n")

            if overclaimed:
                f.write("未授权但声明的权限：\n")
                for p in sorted(overclaimed):
                    f.write(f"  - {p}\n")
                f.write("\n")

            f.write("-" * 80 + "\n\n")

    print(f"\n✅ 分析完成！结果已保存到文件：{output_file}")

def get_app_activities(pkg):
    """获取应用的所有Activity"""
    try:
        result = device.shell(f"dumpsys package {pkg}")
        activities = []
        capture = False

        for line in result.splitlines():
            line = line.strip()
            if "Activity Resolver Table:" in line:
                capture = True
                continue
            elif capture and line.startswith("Non-Data Actions:"):
                break
            elif capture and "/" in line and pkg in line:
                # 提取Activity名称
                if " " in line:
                    activity_part = line.split()[0]
                    if "/" in activity_part and pkg in activity_part:
                        activities.append(activity_part)

        # 如果上面的方法没找到，尝试另一种方法
        if not activities:
            result = device.shell(f"cmd package dump {pkg}")
            for line in result.splitlines():
                if "Activity #" in line and pkg in line:
                    parts = line.split()
                    for part in parts:
                        if "/" in part and pkg in part:
                            activities.append(part)
                            break

        return list(set(activities))  # 去重
    except Exception as e:
        print(f"获取 {pkg} 的Activity失败: {e}")
        return []

def start_activity(activity_name):
    """启动指定的Activity"""
    try:
        result = device.shell(f"am start {activity_name}")
        return "Starting:" in result or "Success" in result
    except Exception as e:
        print(f"启动Activity失败: {e}")
        return False

def interactive_permission_check():
    """交互式权限检测"""
    packages = get_installed_packages()
    print(f"\n📱 发现 {len(packages)} 个应用")
    print("请选择要检测的应用:")

    # 显示应用列表（分页显示）
    page_size = 20
    current_page = 0

    while True:
        start_idx = current_page * page_size
        end_idx = min(start_idx + page_size, len(packages))

        print(f"\n--- 第 {current_page + 1} 页 (共 {(len(packages) + page_size - 1) // page_size} 页) ---")
        for i in range(start_idx, end_idx):
            print(f"{i + 1:3d}. {packages[i]}")

        print("\n操作选项:")
        print("n - 下一页")
        print("p - 上一页")
        print("数字 - 选择应用")
        print("q - 退出")

        choice = input("\n请输入选择: ").strip().lower()

        if choice == 'q':
            return
        elif choice == 'n' and end_idx < len(packages):
            current_page += 1
        elif choice == 'p' and current_page > 0:
            current_page -= 1
        elif choice.isdigit():
            app_idx = int(choice) - 1
            if 0 <= app_idx < len(packages):
                analyze_single_app(packages[app_idx])
                input("\n按回车键继续...")
            else:
                print("无效的应用编号!")

def analyze_single_app(pkg):
    """分析单个应用的权限"""
    print(f"\n🔍 分析应用: {pkg}")
    print("=" * 60)

    declared = get_declared_permissions(pkg)
    granted = get_granted_permissions(pkg)
    overclaimed = set(declared) - set(granted)
    granted_clean = set(granted)

    print(f"📊 权限统计:")
    print(f"  - 声明权限数: {len(declared)}")
    print(f"  - 已授权权限数: {len(granted)}")
    print(f"  - 未授权权限数: {len(overclaimed)}")

    if granted_clean:
        print(f"\n✅ 已授权的权限 ({len(granted_clean)}个):")
        for i, perm in enumerate(sorted(granted_clean), 1):
            print(f"  {i:2d}. {perm}")

    if overclaimed:
        print(f"\n⚠️ 未授权但声明的权限 ({len(overclaimed)}个):")
        for i, perm in enumerate(sorted(overclaimed), 1):
            print(f"  {i:2d}. {perm}")

def launch_all_activities():
    """启动所有应用的所有Activity"""
    packages = get_installed_packages()
    print(f"\n🚀 准备启动所有应用的Activity...")
    print(f"共发现 {len(packages)} 个应用")

    confirm = input("这将尝试启动大量Activity，可能影响设备性能。确认继续? (y/N): ").strip().lower()
    if confirm != 'y':
        print("操作已取消")
        return

    total_activities = 0
    successful_launches = 0
    failed_launches = 0

    import datetime
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = f"activity_launch_log_{timestamp}.txt"

    with open(log_file, 'w', encoding='utf-8') as f:
        f.write(f"Activity启动日志\n")
        f.write(f"启动时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write("=" * 80 + "\n\n")

        for i, pkg in enumerate(packages, 1):
            print(f"\n[{i}/{len(packages)}] 处理应用: {pkg}")
            f.write(f"应用: {pkg}\n")

            activities = get_app_activities(pkg)
            if not activities:
                print(f"  ❌ 未找到Activity")
                f.write("  未找到Activity\n\n")
                continue

            print(f"  📱 发现 {len(activities)} 个Activity")
            f.write(f"  发现 {len(activities)} 个Activity:\n")

            for activity in activities:
                total_activities += 1
                print(f"    启动: {activity}")
                f.write(f"    {activity} - ")

                if start_activity(activity):
                    successful_launches += 1
                    print(f"      ✅ 成功")
                    f.write("成功\n")
                else:
                    failed_launches += 1
                    print(f"      ❌ 失败")
                    f.write("失败\n")

                # 添加短暂延迟避免过快启动
                import time
                time.sleep(0.5)

            f.write("\n")

    print(f"\n📊 启动统计:")
    print(f"总Activity数: {total_activities}")
    print(f"成功启动: {successful_launches}")
    print(f"启动失败: {failed_launches}")
    print(f"成功率: {(successful_launches/total_activities*100):.1f}%")
    print(f"\n📄 详细日志已保存到: {log_file}")

def launch_specific_activities():
    """启动指定应用的Activity"""
    packages = get_installed_packages()
    print(f"\n📱 选择要启动Activity的应用:")

    # 显示应用列表
    for i, pkg in enumerate(packages[:20], 1):  # 只显示前20个
        print(f"{i:2d}. {pkg}")

    if len(packages) > 20:
        print(f"... 还有 {len(packages) - 20} 个应用")

    choice = input("\n请输入应用编号 (或输入完整包名): ").strip()

    if choice.isdigit():
        app_idx = int(choice) - 1
        if 0 <= app_idx < min(20, len(packages)):
            selected_pkg = packages[app_idx]
        else:
            print("无效的应用编号!")
            return
    else:
        # 尝试作为包名处理
        if choice in packages:
            selected_pkg = choice
        else:
            print("未找到指定的应用包名!")
            return

    print(f"\n🔍 获取 {selected_pkg} 的Activity...")
    activities = get_app_activities(selected_pkg)

    if not activities:
        print("❌ 未找到任何Activity")
        return

    print(f"\n📱 发现 {len(activities)} 个Activity:")
    for i, activity in enumerate(activities, 1):
        print(f"{i:2d}. {activity}")

    print(f"\n选项:")
    print("a - 启动所有Activity")
    print("数字 - 启动指定Activity")
    print("q - 退出")

    choice = input("\n请选择: ").strip().lower()

    if choice == 'q':
        return
    elif choice == 'a':
        print(f"\n🚀 启动 {selected_pkg} 的所有Activity...")
        successful = 0
        for activity in activities:
            print(f"启动: {activity}")
            if start_activity(activity):
                successful += 1
                print("  ✅ 成功")
            else:
                print("  ❌ 失败")
            import time
            time.sleep(0.5)
        print(f"\n完成! 成功启动 {successful}/{len(activities)} 个Activity")
    elif choice.isdigit():
        activity_idx = int(choice) - 1
        if 0 <= activity_idx < len(activities):
            activity = activities[activity_idx]
            print(f"\n🚀 启动: {activity}")
            if start_activity(activity):
                print("✅ 启动成功!")
            else:
                print("❌ 启动失败!")
        else:
            print("无效的Activity编号!")

def generate_summary_report():
    """生成权限分析摘要报告"""
    packages = get_installed_packages()
    print(f"正在生成摘要报告...")

    import datetime
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    summary_file = f"android_permissions_summary_{timestamp}.txt"

    total_apps = len(packages)
    apps_with_overclaimed = 0
    total_declared = 0
    total_granted = 0
    total_overclaimed = 0

    high_risk_apps = []  # 声明权限过多的应用

    with open(summary_file, 'w', encoding='utf-8') as f:
        f.write(f"Android 应用权限分析摘要报告\n")
        f.write(f"分析时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"共分析 {total_apps} 个应用\n")
        f.write("=" * 80 + "\n\n")

        for pkg in packages:
            declared = get_declared_permissions(pkg)
            granted = get_granted_permissions(pkg)
            overclaimed = set(declared) - set(granted)

            total_declared += len(declared)
            total_granted += len(granted)
            total_overclaimed += len(overclaimed)

            if overclaimed:
                apps_with_overclaimed += 1

            # 标记高风险应用（声明权限超过50个）
            if len(declared) > 50:
                high_risk_apps.append({
                    'pkg': pkg,
                    'declared': len(declared),
                    'granted': len(granted),
                    'overclaimed': len(overclaimed)
                })

        # 写入统计摘要
        f.write("📊 权限使用统计\n")
        f.write("-" * 40 + "\n")
        f.write(f"总应用数: {total_apps}\n")
        f.write(f"有未授权权限的应用数: {apps_with_overclaimed}\n")
        f.write(f"总声明权限数: {total_declared}\n")
        f.write(f"总已授权权限数: {total_granted}\n")
        f.write(f"总未授权权限数: {total_overclaimed}\n")
        f.write(f"权限授权率: {(total_granted/total_declared*100):.1f}%\n\n")

        # 写入高风险应用
        f.write("⚠️ 高风险应用 (声明权限>50个)\n")
        f.write("-" * 40 + "\n")
        high_risk_apps.sort(key=lambda x: x['declared'], reverse=True)
        for app in high_risk_apps[:20]:  # 只显示前20个
            f.write(f"📦 {app['pkg']}\n")
            f.write(f"   声明: {app['declared']} | 已授权: {app['granted']} | 未授权: {app['overclaimed']}\n")
        f.write(f"\n共发现 {len(high_risk_apps)} 个高风险应用\n\n")

        # 控制台输出摘要
        print(f"\n📊 权限分析摘要:")
        print(f"总应用数: {total_apps}")
        print(f"有未授权权限的应用数: {apps_with_overclaimed}")
        print(f"权限授权率: {(total_granted/total_declared*100):.1f}%")
        print(f"高风险应用数: {len(high_risk_apps)}")

    print(f"\n✅ 摘要报告已保存到: {summary_file}")

if __name__ == "__main__":
    while True:
        print("\n" + "="*60)
        print("🔧 Android 应用权限分析与Activity启动工具")
        print("="*60)
        print("1. 详细权限分析 (生成完整报告)")
        print("2. 摘要权限分析 (生成简洁摘要)")
        print("3. 交互式权限检测 (选择特定应用)")
        print("4. 启动指定应用的Activity")
        print("5. 启动所有应用的所有Activity (⚠️ 慎用)")
        print("6. 退出程序")
        print("-"*60)

        choice = input("请输入选择 (1-6): ").strip()

        if choice == "1":
            print("\n🔍 开始详细权限分析...")
            analyze_permissions()
        elif choice == "2":
            print("\n📊 开始摘要权限分析...")
            generate_summary_report()
        elif choice == "3":
            interactive_permission_check()
        elif choice == "4":
            launch_specific_activities()
        elif choice == "5":
            launch_all_activities()
        elif choice == "6":
            print("\n👋 程序已退出")
            break
        else:
            print("\n❌ 无效选择，请重新输入!")

        if choice in ["1", "2", "3", "4", "5"]:
            input("\n按回车键返回主菜单...")
