import adbutils
import subprocess
import re

# 初始化 ADB 设备
adb = adbutils.AdbClient(host="127.0.0.1", port=5037)
device = adb.device()

def get_installed_packages():
    """获取所有已安装包名"""
    result = device.shell("pm list packages")
    packages = [line.replace("package:", "").strip() for line in result.splitlines()]
    return packages

def get_declared_permissions(pkg):
    """获取 Manifest 中声明的权限"""
    result = device.shell(f"dumpsys package {pkg}")
    permissions = []
    capture = False
    for line in result.splitlines():
        if "requested permissions:" in line:
            capture = True
            continue
        if capture:
            if line.strip() == "":
                break
            match = re.search(r'(\S+)', line.strip())
            if match:
                permissions.append(match.group(1))
    return permissions

def get_granted_permissions(pkg):
    """获取当前已授权的权限"""
    result = device.shell(f"dumpsys package {pkg}")
    granted = []
    capture = False
    for line in result.splitlines():
        if "install permissions:" in line:
            capture = True
            continue
        if capture:
            if line.strip() == "":
                break
            match = re.search(r'granted=(true).*?(\S+)', line)
            if match:
                granted.append(match.group(2))
    return granted

def analyze_permissions():
    packages = get_installed_packages()
    print(f"共发现 {len(packages)} 个应用，开始分析权限...\n")

    for pkg in packages:
        declared = get_declared_permissions(pkg)
        granted = get_granted_permissions(pkg)
        overclaimed = set(declared) - set(granted)

        print(f"📦 应用：{pkg}")
        print(f"  - 声明权限数：{len(declared)}")
        print(f"  - 已授权权限数：{len(granted)}")
        if overclaimed:
            print(f"  ⚠️ 未授权但声明的权限：")
            for p in overclaimed:
                print(f"    - {p}")
        print("-" * 40)

if __name__ == "__main__":
    analyze_permissions()
